version: 6
environments:
  default:
    channels:
    - url: https://conda.modular.com/max-nightly/
    - url: https://conda.anaconda.org/conda-forge/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiofiles-24.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.12.14-py313h3dea7bd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/annotated-types-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.9.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asgiref-3.9.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-h0fbd49f_19.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.2-he7b75e1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.4-hb03c661_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-h92c474e_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.5-h149bd38_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.4-h37a7233_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.21.2-h6252d9a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.3-h19deb91_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.8.6-h800fcd2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.4-h92c474e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-h92c474e_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.33.1-hb4fd278_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.606-h31ade35_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.16.0-h3a458e0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.12.0-ha729027_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.14.0-hb1c9500_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.10.0-hebae86a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-h8b27e44_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backoff-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py313h46c70d0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py313hfab6e84_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/datasets-3.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/deprecated-1.2.18-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/dnspython-2.7.0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/email-validator-2.2.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/email_validator-2.2.0-hd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-0.115.14-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-cli-0.0.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.7.0-py313h6b9daa2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/gguf-0.17.1-pyhc364b38_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py313h11186cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/googleapis-common-protos-1.70.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-1.73.1-py313h32f65e6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hf-transfer-0.1.9-py313hbd55b0c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hf-xet-1.1.5-py39h260a9e5_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/httptools-0.6.4-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250512.1-cxx17_hba17884_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-21.0.0-hd5bb725_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-21.0.0-h635bf11_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-compute-21.0.0-he319acf_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-21.0.0-h635bf11_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-21.0.0-h3f74fd7_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.39.0-hdb79228_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.39.0-hdbdcf42_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.73.1-h1e535eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h3d81e11_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.21.0-hb9b0907_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.21.0-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-21.0.0-h790f06f_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-6.31.1-h9ef548d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2025.07.22-h7b12aa8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsentencepiece-0.2.0-hb747028_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.3-hee844dc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.22.0-h454ac66_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.1-cpu_mkl_hf38bc2d_103.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h202a827_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llguidance-1.1.1-py39hcb7a38b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.8-h4922eb0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py313h8060acc_1.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/max-pipelines-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025072805-release.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha770c72_16.conda
      - conda: https://conda.modular.com/max-nightly/noarch/modular-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-25.5.0.dev2025072805-release.conda
      - conda: https://conda.modular.com/max-nightly/linux-64/mojo-dev-25.5.0.dev2025072805-release.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/msgspec-0.19.0-py313h536fd9c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.6.3-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.16-py313h536fd9c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py313hf6604e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.1-h7b32b05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-api-1.35.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-common-1.35.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-http-1.35.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-prometheus-0.56b0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-proto-1.35.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-sdk-1.35.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-semantic-conventions-0.56b0-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.17.0-py313h7037e92_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.3-h61e0c1e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.1-py313h08cd8bf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py313h8db990d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/protobuf-6.31.1-py313hc6d18d0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-21.0.0-py313h78bf25f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-21.0.0-py313he109ebe_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-3.0.0-pyh9380348_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-3.0.0-pyhf748d72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-2.11.7-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pydantic-core-2.33.2-py313h4b2b08d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-settings-2.10.1-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyinstrument-5.0.3-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dotenv-1.1.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-multipart-0.0.20-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py313h536fd9c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-8_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.1-cpu_mkl_py313_h58dab0e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py313h8060acc_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2025.07.22-h5a314c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-toolkit-0.14.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.23-h8e187f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py313h920b4c0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.0-py313h86fcf2b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-0.2.0-h9ebfe73_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-python-0.2.0-py313h4d3dc0d_12.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-spm-0.2.0-hb747028_12.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.2-h03e3b7b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sse-starlette-2.1.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/starlette-0.41.2-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/taskgroup-0.2.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.3-py313h1191936_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-inspection-0.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-0.35.0-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-standard-0.35.0-h31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/uvloop-0.21.0-py313h536fd9c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/watchfiles-1.1.0-py313h920b4c0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/websockets-15.0.1-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wrapt-1.17.2-py313h536fd9c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h280c20c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.1-py313h8060acc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py313h536fd9c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
  build_number: 3
  sha256: cec7343e76c9da6a42c7e7cba53391daa6b46155054ef61a5ef522ea27c5a058
  md5: ee5c2118262e30b972bc0b4db8ef0ba5
  depends:
  - llvm-openmp >=9.0.1
  license: BSD-3-Clause
  license_family: BSD
  size: 7649
  timestamp: 1741390353130
- conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
  sha256: a3967b937b9abf0f2a99f3173fa4630293979bd1644709d89580e7c62a544661
  md5: aaa2a381ccc56eac91d63b6c1240312f
  depends:
  - cpython
  - python-gil
  license: MIT
  license_family: MIT
  size: 8191
  timestamp: 1744137672556
- conda: https://conda.anaconda.org/conda-forge/noarch/aiofiles-24.1.0-pyhd8ed1ab_1.conda
  sha256: 8e18809f00b0bfe504bc6180b80d844016690925ddf0e61272111eec079774c3
  md5: 7e8045a75e921648c082ba7cd7edd114
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 19712
  timestamp: 1733829125632
- conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
  sha256: 7842ddc678e77868ba7b92a726b437575b23aaec293bca0d40826f1026d90e27
  md5: 18fd895e0e775622906cdabfc3cf0fb4
  depends:
  - python >=3.9
  license: PSF-2.0
  license_family: PSF
  size: 19750
  timestamp: 1741775303303
- conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.12.14-py313h3dea7bd_0.conda
  sha256: e4829f9acee8b5e99fc1d2c68c365449b47aba009d5d7756f96a73e602382844
  md5: 860893cafaf1b7c06bac07c2ef10eed0
  depends:
  - __glibc >=2.17,<3.0.a0
  - aiohappyeyeballs >=2.5.0
  - aiosignal >=1.4.0
  - attrs >=17.3.0
  - frozenlist >=1.1.1
  - libgcc >=14
  - multidict >=4.5,<7.0
  - propcache >=0.2.0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - yarl >=1.17.0,<2.0
  license: MIT AND Apache-2.0
  license_family: Apache
  size: 1012655
  timestamp: 1752162717040
- conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.4.0-pyhd8ed1ab_0.conda
  sha256: 8dc149a6828d19bf104ea96382a9d04dae185d4a03cc6beb1bc7b84c428e3ca2
  md5: 421a865222cd0c9d83ff08bc78bf3a61
  depends:
  - frozenlist >=1.1.0
  - python >=3.9
  - typing_extensions >=4.2
  license: Apache-2.0
  license_family: APACHE
  size: 13688
  timestamp: 1751626573984
- conda: https://conda.anaconda.org/conda-forge/noarch/annotated-types-0.7.0-pyhd8ed1ab_1.conda
  sha256: e0ea1ba78fbb64f17062601edda82097fcf815012cf52bb704150a2668110d48
  md5: 2934f256a8acfe48f6ebb4fce6cde29c
  depends:
  - python >=3.9
  - typing-extensions >=4.0.0
  license: MIT
  license_family: MIT
  size: 18074
  timestamp: 1733247158254
- conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.9.0-pyh29332c3_0.conda
  sha256: b28e0f78bb0c7962630001e63af25a89224ff504e135a02e50d4d80b6155d386
  md5: 9749a2c77a7c40d432ea0927662d7e52
  depends:
  - exceptiongroup >=1.0.2
  - idna >=2.8
  - python >=3.9
  - sniffio >=1.1
  - typing_extensions >=4.5
  - python
  constrains:
  - trio >=0.26.1
  - uvloop >=0.21
  license: MIT
  license_family: MIT
  size: 126346
  timestamp: 1742243108743
- conda: https://conda.anaconda.org/conda-forge/noarch/asgiref-3.9.1-pyhd8ed1ab_0.conda
  sha256: 901e1739180f8c33d50e15ee509c4c751fdc45f6a1c25cd88238d887933af938
  md5: bc48d02f0a7de827257eb101527439e8
  depends:
  - python >=3.9
  - typing_extensions >=4
  license: BSD-3-Clause
  license_family: BSD
  size: 26898
  timestamp: 1751974695566
- conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
  sha256: 99c53ffbcb5dc58084faf18587b215f9ac8ced36bbfb55fa807c00967e419019
  md5: a10d11958cadc13fdb43df75f8b1903f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 57181
  timestamp: 1741918625732
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-h0fbd49f_19.conda
  sha256: 02bb7d2b21f60591944d97c2299be53c9c799085d0a1fb15620d5114cf161c3a
  md5: 24139f2990e92effbeb374a0eb33fdb1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - aws-c-io >=0.21.2,<0.21.3.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-http >=0.10.4,<0.10.5.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 122970
  timestamp: 1753305744902
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.2-he7b75e1_1.conda
  sha256: 30ecca069fdae0aa6a8bb64c47eb5a8d9a7bef7316181e8cbb08b7cb47d8b20f
  md5: c04d1312e7feec369308d656c18e7f3e
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - libgcc >=14
  - openssl >=3.5.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 50942
  timestamp: 1752240577225
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.4-hb03c661_0.conda
  sha256: 6c9e1b9e82750c39ac0251dcfbeebcbb00a1af07c0d7e3fb1153c4920da316eb
  md5: ae5621814cb99642c9308977fe90ed0d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  license: Apache-2.0
  license_family: Apache
  size: 236420
  timestamp: 1752193614294
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-h92c474e_6.conda
  sha256: 154d4a699f4d8060b7f2cec497a06e601cbd5c8cde6736ced0fb7e161bc6f1bb
  md5: 3490e744cb8b9d5a3b9785839d618a17
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - aws-c-common >=0.12.4,<0.12.5.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 22116
  timestamp: 1752240005329
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.5-h149bd38_3.conda
  sha256: 74b7e5d727505efdb1786d9f4e0250484d23934a1d87f234dacacac97e440136
  md5: f9bff8c2a205ee0f28b0c61dad849a98
  depends:
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - aws-c-io >=0.21.2,<0.21.3.0a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 57675
  timestamp: 1753199060663
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.4-h37a7233_0.conda
  sha256: 6794d020d75cafa15e7677508c4bea5e8bca6233a5c7eb6c34397367ee37024c
  md5: d828cb0be64d51e27eebe354a2907a98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-io >=0.21.2,<0.21.3.0a0
  - aws-c-compression >=0.3.1,<0.3.2.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 224186
  timestamp: 1753205774708
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.21.2-h6252d9a_1.conda
  sha256: 01ab3fd74ccd1cd3ebdde72898e0c3b9ab23151b9cd814ac627e3efe88191d8e
  md5: cf5e9b21384fdb75b15faf397551c247
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - s2n >=1.5.23,<1.5.24.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  license: Apache-2.0
  size: 180168
  timestamp: 1753465862916
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.3-h19deb91_3.conda
  sha256: 4f1b36a50f9d74267cc73740af252f1d6f2da21a6dbef3c0086df1a78c81ed6f
  md5: 1680d64986f8263978c3624f677656c8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - aws-c-io >=0.21.2,<0.21.3.0a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - aws-c-http >=0.10.4,<0.10.5.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 216117
  timestamp: 1753306261844
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.8.6-h800fcd2_2.conda
  sha256: 886345904f41cdcd8ca4a540161d471d18de60871ffcce42242a4812fc90dcea
  md5: 50e0900a33add0c715f17648de6be786
  depends:
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - aws-c-http >=0.10.4,<0.10.5.0a0
  - openssl >=3.5.1,<4.0a0
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-io >=0.21.2,<0.21.3.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 137514
  timestamp: 1753335820784
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.4-h92c474e_1.conda
  sha256: a9e071a584be0257b2ec6ab6e1f203e9d6b16d2da2233639432727ffbf424f3d
  md5: 4ab554b102065910f098f88b40163835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - aws-c-common >=0.12.4,<0.12.5.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 59146
  timestamp: 1752240966518
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-h92c474e_2.conda
  sha256: 7168007329dfb1c063cd5466b33a1f2b8a28a00f587a0974d97219432361b4db
  md5: 248831703050fe9a5b2680a7589fdba9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - aws-c-common >=0.12.4,<0.12.5.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 76748
  timestamp: 1752241068761
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.33.1-hb4fd278_2.conda
  sha256: 530384aec79a46adbe59e9c20f0c8ec14227aaf4ea2d2b53a30bca8dcbe35309
  md5: 81c545e27e527ca1be0cc04b74c20386
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - aws-c-cal >=0.9.2,<0.9.3.0a0
  - aws-c-http >=0.10.4,<0.10.5.0a0
  - aws-c-s3 >=0.8.6,<0.8.7.0a0
  - aws-c-event-stream >=0.5.5,<0.5.6.0a0
  - aws-c-io >=0.21.2,<0.21.3.0a0
  - aws-c-mqtt >=0.13.3,<0.13.4.0a0
  - aws-c-sdkutils >=0.2.4,<0.2.5.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 406263
  timestamp: 1753342146233
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.606-h31ade35_1.conda
  sha256: f2a6c653c4803e0edb11054d21395d53624ef9ad330d09c692a4dae638c399a4
  md5: e33b3d2a2d44ba0fb35373d2343b71dd
  depends:
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.14.1,<9.0a0
  - libzlib >=1.3.1,<2.0a0
  - aws-c-common >=0.12.4,<0.12.5.0a0
  - aws-c-event-stream >=0.5.5,<0.5.6.0a0
  - aws-crt-cpp >=0.33.1,<0.33.2.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 3367142
  timestamp: 1752920616764
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.16.0-h3a458e0_0.conda
  sha256: bd28c90012b063a1733d85a19f83e046f9839ea000e77ecbcac8a87b47d4fb53
  md5: c09adf9bb0f9310cf2d7af23a4fbf1ff
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.14.1,<9.0a0
  - libgcc >=14
  - libstdcxx >=14
  - openssl >=3.5.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 348296
  timestamp: 1752514821753
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.12.0-ha729027_0.conda
  sha256: 734857814400585dca2bee2a4c2e841bc89c143bf0dcc11b4c7270cea410650c
  md5: 3dab8d6fa3d10fe4104f1fbe59c10176
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.16.0,<1.16.1.0a0
  - libgcc >=14
  - libstdcxx >=14
  - openssl >=3.5.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 241853
  timestamp: 1753212593417
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.14.0-hb1c9500_1.conda
  sha256: 83cea4a570a457cc18571c92d7927e6cc4ea166f0f819f0b510d4e2c8daf112d
  md5: 30da390c211967189c58f83ab58a6f0c
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.16.0,<1.16.1.0a0
  - azure-storage-common-cpp >=12.10.0,<12.10.1.0a0
  - libgcc >=14
  - libstdcxx >=14
  license: MIT
  license_family: MIT
  size: 577592
  timestamp: 1753219590665
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.10.0-hebae86a_2.conda
  sha256: 071536dc90aa0ea22a5206fbac5946c70beec34315ab327c4379983e7da60196
  md5: 0d93ce986d13e46a8fc91c289597d78f
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.16.0,<1.16.1.0a0
  - libgcc >=14
  - libstdcxx >=14
  - libxml2 >=2.13.8,<2.14.0a0
  - openssl >=3.5.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 148875
  timestamp: 1753211824276
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-h8b27e44_3.conda
  sha256: aec2e2362a605e37a38c4b34f191e98dd33fdc64ce4feebd60bd0b4d877ab36b
  md5: 7b738aea4f1b8ae2d1118156ad3ae993
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.16.0,<1.16.1.0a0
  - azure-storage-blobs-cpp >=12.14.0,<12.14.1.0a0
  - azure-storage-common-cpp >=12.10.0,<12.10.1.0a0
  - libgcc >=14
  - libstdcxx >=14
  license: MIT
  license_family: MIT
  size: 299871
  timestamp: 1753226720130
- conda: https://conda.anaconda.org/conda-forge/noarch/backoff-2.2.1-pyhd8ed1ab_1.conda
  sha256: f334115c6b0c6c2cd0d28595365f205ec7eaa60bcc5ff91a75d7245f728be820
  md5: a38b801f2bcc12af80c2e02a9e4ce7d9
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 18816
  timestamp: 1733771192649
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py313h46c70d0_3.conda
  sha256: e510ad1db7ea882505712e815ff02514490560fd74b5ec3a45a6c7cf438f754d
  md5: 2babfedd9588ad40c7113ddfe6a5ca82
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  license: MIT
  license_family: MIT
  size: 350295
  timestamp: 1749230225293
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
  sha256: f8003bef369f57396593ccd03d08a8e21966157269426f71e943f96e4b579aeb
  md5: f7f0d6cc2dc986d42ac2689ec88192be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 206884
  timestamp: 1744127994291
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
  sha256: 29defbd83c7829788358678ec996adeee252fa4d4274b7cd386c1ed73d2b201e
  md5: d16c90324aef024877d8713c0b7fea5b
  depends:
  - __unix
  license: ISC
  size: 155658
  timestamp: 1752482350666
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
  sha256: f68ee5038f37620a4fb4cdd8329c9897dce80331db8c94c3ab264a26a8c70a08
  md5: 4c07624f3faefd0bb6659fb7396cfa76
  depends:
  - python >=3.9
  license: ISC
  size: 159755
  timestamp: 1752493370797
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py313hfab6e84_0.conda
  sha256: 73cd6199b143a8a6cbf733ce124ed57defc1b9a7eab9b10fd437448caf8eaa45
  md5: ce6386a5892ef686d6d680c345c40ad1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 295514
  timestamp: 1725560706794
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
  sha256: 535ae5dcda8022e31c6dc063eb344c80804c537a5a04afba43a845fa6fa130f5
  md5: 40fe4284b8b5835a9073a645139f35af
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 50481
  timestamp: 1746214981991
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.13.5-py313hd8ed1ab_102.conda
  noarch: generic
  sha256: 058c8156ff880b1180a36b94307baad91f9130d0e3019ad8c7ade035852016fb
  md5: 0401f31e3c9e48cebf215472aa3e7104
  depends:
  - python >=3.13,<3.14.0a0
  - python_abi * *_cp313
  license: Python-2.0
  size: 47560
  timestamp: 1750062514868
- conda: https://conda.anaconda.org/conda-forge/noarch/datasets-3.6.0-pyhd8ed1ab_0.conda
  sha256: 0218079382123518bfa9b461833996d5c3afb541c6e3ab9e41a24ec4bc5b15f6
  md5: 39b0719f7ade25bda275efe4b6973486
  depends:
  - aiohttp
  - dill >=0.3.0,<0.3.9
  - filelock
  - fsspec >=2023.1.0,<=2025.3.0
  - huggingface_hub >=0.24.0
  - multiprocess <0.70.17
  - numpy >=1.17
  - packaging
  - pandas
  - pyarrow >=15.0.0
  - python >=3.9
  - python-xxhash
  - pyyaml >=5.1
  - requests >=2.32.2
  - tqdm >=4.66.3
  license: Apache-2.0
  license_family: Apache
  size: 338869
  timestamp: 1746740579822
- conda: https://conda.anaconda.org/conda-forge/noarch/deprecated-1.2.18-pyhd8ed1ab_0.conda
  sha256: d614bcff10696f1efc714df07651b50bf3808401fcc03814309ecec242cc8870
  md5: 0cef44b1754ae4d6924ac0eef6b9fdbe
  depends:
  - python >=3.9
  - wrapt <2,>=1.10
  license: MIT
  license_family: MIT
  size: 14382
  timestamp: 1737987072859
- conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.8-pyhd8ed1ab_0.conda
  sha256: 482b5b566ca559119b504c53df12b08f3962a5ef8e48061d62fd58a47f8f2ec4
  md5: 78745f157d56877a2c6e7b386f66f3e2
  depends:
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  size: 88169
  timestamp: 1706434833883
- conda: https://conda.anaconda.org/conda-forge/noarch/dnspython-2.7.0-pyhff2d567_1.conda
  sha256: 3ec40ccf63f2450c5e6c7dd579e42fc2e97caf0d8cd4ba24aa434e6fc264eda0
  md5: 5fbd60d61d21b4bd2f9d7a48fe100418
  depends:
  - python >=3.9,<4.0.0
  - sniffio
  constrains:
  - aioquic >=1.0.0
  - wmi >=1.5.1
  - httpx >=0.26.0
  - trio >=0.23
  - cryptography >=43
  - httpcore >=1.0.0
  - idna >=3.7
  - h2 >=4.1.0
  license: ISC
  license_family: OTHER
  size: 172172
  timestamp: 1733256829961
- conda: https://conda.anaconda.org/conda-forge/noarch/email-validator-2.2.0-pyhd8ed1ab_1.conda
  sha256: b91a19eb78edfc2dbb36de9a67f74ee2416f1b5273dd7327abe53f2dbf864736
  md5: da16dd3b0b71339060cd44cb7110ddf9
  depends:
  - dnspython >=2.0.0
  - idna >=2.0.0
  - python >=3.9
  license: Unlicense
  size: 44401
  timestamp: 1733300827551
- conda: https://conda.anaconda.org/conda-forge/noarch/email_validator-2.2.0-hd8ed1ab_1.conda
  sha256: e0d0fdf587aa0ed0ff08b2bce3ab355f46687b87b0775bfba01cc80a859ee6a2
  md5: 0794f8807ff2c6f020422cacb1bd7bfa
  depends:
  - email-validator >=2.2.0,<2.2.1.0a0
  license: Unlicense
  size: 6552
  timestamp: 1733300828176
- conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
  sha256: ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca
  md5: 72e42d28960d875c7654614f8b50939a
  depends:
  - python >=3.9
  - typing_extensions >=4.6.0
  license: MIT and PSF-2.0
  size: 21284
  timestamp: 1746947398083
- conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-0.115.14-pyhe01879c_0.conda
  sha256: 4e1d1aabe3199033c9c5a47176b0b4e0cd40621156fc72f706047c2348dd72ff
  md5: 8f4fcc62c241e372495c19fe6f8b1908
  depends:
  - python >=3.9
  - starlette >=0.40.0,<0.47.0
  - typing_extensions >=4.8.0
  - pydantic >=1.7.4,!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0
  - email_validator >=2.0.0
  - fastapi-cli >=0.0.5
  - httpx >=0.23.0
  - jinja2 >=3.1.5
  - python-multipart >=0.0.18
  - uvicorn-standard >=0.12.0
  - python
  license: MIT
  license_family: MIT
  size: 78363
  timestamp: 1750986285010
- conda: https://conda.anaconda.org/conda-forge/noarch/fastapi-cli-0.0.8-pyhd8ed1ab_0.conda
  sha256: 71bfe707fa15af98e62d1023a6f3a670b006cf22ee970f227478ebd2cccca092
  md5: 7b4fa933822891d1ce36e3dda98e0e38
  depends:
  - python >=3.9
  - rich-toolkit >=0.14.8
  - typer >=0.15.1
  - uvicorn >=0.15.0
  - uvicorn-standard >=0.15.0
  license: MIT
  license_family: MIT
  size: 16130
  timestamp: 1751972177481
- conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
  sha256: de7b6d4c4f865609ae88db6fa03c8b7544c2452a1aa5451eb7700aad16824570
  md5: 4547b39256e296bb758166893e909a7c
  depends:
  - python >=3.9
  license: Unlicense
  size: 17887
  timestamp: 1741969612334
- conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.7.0-py313h6b9daa2_0.conda
  sha256: 0742b58b7d685e67bf822f0b84a9e52473de071412d21453ad19ee187a4a6cf7
  md5: 3a0be7abedcbc2aee92ea228efea8eba
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 54659
  timestamp: 1752167252322
- conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.3.0-pyhd8ed1ab_0.conda
  sha256: 9cbba3b36d1e91e4806ba15141936872d44d20a4d1e3bb74f4aea0ebeb01b205
  md5: 5ecafd654e33d1f2ecac5ec97057593b
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 141329
  timestamp: 1741404114588
- conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
  sha256: 6c33bf0c4d8f418546ba9c250db4e4221040936aef8956353bc764d4877bc39a
  md5: d411fc29e338efb48c5fd4576d71d881
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 119654
  timestamp: 1726600001928
- conda: https://conda.anaconda.org/conda-forge/noarch/gguf-0.17.1-pyhc364b38_0.conda
  sha256: 06aa364c6ce109e21858fc016a430c22f738fe6377c67944504df7fc0da3ec20
  md5: aaaa7074fd79c4e1e79b3e1af5a77efa
  depends:
  - python >=3.8
  - numpy >=1.17
  - tqdm >=4.27
  - pyyaml >=5.1
  - sentencepiece >=0.1.98,<=0.2.0
  - python
  license: MIT
  license_family: MIT
  size: 92085
  timestamp: 1750400728782
- conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
  sha256: dc824dc1d0aa358e28da2ecbbb9f03d932d976c8dca11214aa1dcdfcbd054ba2
  md5: ff862eebdfeb2fd048ae9dc92510baca
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 143452
  timestamp: 1718284177264
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py313h11186cd_0.conda
  sha256: 1f66faf02d062348148afb7eb86fa5baf011afd5e826884e20c378e79a0d6174
  md5: 54d020e0eaacf1e99bfb2410b9aa2e5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpc >=1.3.1,<2.0a0
  - mpfr >=4.2.1,<5.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 213289
  timestamp: 1745509587714
- conda: https://conda.anaconda.org/conda-forge/noarch/googleapis-common-protos-1.70.0-pyhd8ed1ab_0.conda
  sha256: e0aa51de5565e92139791c5b8e2908e3cadd2c5fce6941a225889070815bcd99
  md5: 7999fb45c48645272d7d88de0b7dc188
  depends:
  - protobuf >=3.20.2,<7.0.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 142129
  timestamp: 1744688907411
- conda: https://conda.anaconda.org/conda-forge/linux-64/grpcio-1.73.1-py313h32f65e6_0.conda
  sha256: ee1838fb0b8af898dd69a36a362487f4a2c3c0b5dba9fa024b5387eebd97a4e7
  md5: 621cb5cee7910da2c54626189d3b6d42
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgrpc 1.73.1 h1e535eb_0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 894207
  timestamp: 1751746916084
- conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
  sha256: f64b68148c478c3bfc8f8d519541de7d2616bf59d44485a5271041d40c061887
  md5: 4b69232755285701bc86a5afe4d9933a
  depends:
  - python >=3.9
  - typing_extensions
  license: MIT
  license_family: MIT
  size: 37697
  timestamp: 1745526482242
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
  sha256: 0aa1cdc67a9fe75ea95b5644b734a756200d6ec9d0dff66530aec3d1c1e9df75
  md5: b4754fb1bdcb70c8fd54f918301582c6
  depends:
  - hpack >=4.1,<5
  - hyperframe >=6.1,<7
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 53888
  timestamp: 1738578623567
- conda: https://conda.anaconda.org/conda-forge/linux-64/hf-transfer-0.1.9-py313hbd55b0c_1.conda
  sha256: b6103d02cef95f6f60905236582dd3c2432eaff0c05301231ed7af2002591e55
  md5: 697792246585f1567abdf94f36aac24f
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.13.* *_cp313
  - openssl >=3.4.1,<4.0a0
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 1338863
  timestamp: 1739803762596
- conda: https://conda.anaconda.org/conda-forge/linux-64/hf-xet-1.1.5-py39h260a9e5_3.conda
  noarch: python
  sha256: b28905ff975bd935cd113ee97b7eb5b5e3b0969a21302135c6ae096aa06a61f6
  md5: 7b6007f4ad18a970ca3a977148cf47de
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  - _python_abi3_support 1.*
  - cpython >=3.9
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2537615
  timestamp: 1750541218448
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
  sha256: 04d49cb3c42714ce533a8553986e1642d0549a05dc5cc48e0d43ff5be6679a5b
  md5: 4f14640d58e2cc0aa0819d9d8ba125bb
  depends:
  - python >=3.9
  - h11 >=0.16
  - h2 >=3,<5
  - sniffio 1.*
  - anyio >=4.0,<5.0
  - certifi
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 49483
  timestamp: 1745602916758
- conda: https://conda.anaconda.org/conda-forge/linux-64/httptools-0.6.4-py313h536fd9c_0.conda
  sha256: bb469a6efa00fad5ea8454ecb32a94f98a7f51b31540e4f14864beb363532aa8
  md5: 0a59582d8f8d588916b411e4b88db32e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT
  license_family: MIT
  size: 99180
  timestamp: 1732707773985
- conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
  sha256: cd0f1de3697b252df95f98383e9edb1d00386bfdd03fdf607fa42fe5fcb09950
  md5: d6989ead454181f4f9bc987d3dc4e285
  depends:
  - anyio
  - certifi
  - httpcore 1.*
  - idna
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 63082
  timestamp: 1733663449209
- conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.33.4-pyhd8ed1ab_0.conda
  sha256: 3aa04a293974f6e0a3685c085d405ba95e439e1d5a51956c614243bd209da147
  md5: 6c1dddeb4310cf07c0c9bb5f94aa9c9a
  depends:
  - filelock
  - fsspec >=2023.5.0
  - hf-xet >=1.1.2,<2.0.0
  - packaging >=20.9
  - python >=3.9
  - pyyaml >=5.1
  - requests
  - tqdm >=4.42.1
  - typing-extensions >=3.7.4.3
  - typing_extensions >=3.7.4.3
  license: Apache-2.0
  license_family: APACHE
  size: 318652
  timestamp: 1752515004798
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
  sha256: f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af
  md5: 446bd6c8cb26050d528881df495ce646
  depends:
  - markupsafe >=2.0
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 112714
  timestamp: 1741263433881
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
  sha256: 56a7a7e907f15cca8c4f9b0c99488276d4cb10821d2d15df9245662184872e81
  md5: b7d89d860ebcda28a5303526cdee68ab
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 59562
  timestamp: 1748333186063
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
  sha256: d6a61830a354da022eae93fa896d0991385a875c6bba53c82263a289deda9db8
  md5: 000e85703f0fd9594c81710dd5066471
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 248046
  timestamp: 1739160907615
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
  sha256: 1a620f27d79217c1295049ba214c2f80372062fd251b569e9873d4a953d27554
  md5: 0be7c6e070c19105f966d3758448d018
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.44
  license: GPL-3.0-only
  license_family: GPL
  size: 676044
  timestamp: 1752032747103
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250512.1-cxx17_hba17884_0.conda
  sha256: dcd1429a1782864c452057a6c5bc1860f2b637dc20a2b7e6eacd57395bbceff8
  md5: 83b160d4da3e1e847bf044997621ed63
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - libabseil-static =20250512.1=cxx17*
  - abseil-cpp =20250512.1
  license: Apache-2.0
  license_family: Apache
  size: 1310612
  timestamp: 1750194198254
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-21.0.0-hd5bb725_0_cpu.conda
  sha256: 430ee09329c0f0c54d5f0f290558823988d70c1ba4767c0d43e273106ead79f1
  md5: e4b094a4c46fd7c598c2ff78e0080ba7
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-crt-cpp >=0.33.1,<0.33.2.0a0
  - aws-sdk-cpp >=1.11.606,<1.11.607.0a0
  - azure-core-cpp >=1.16.0,<1.16.1.0a0
  - azure-identity-cpp >=1.12.0,<1.12.1.0a0
  - azure-storage-blobs-cpp >=12.14.0,<12.14.1.0a0
  - azure-storage-files-datalake-cpp >=12.12.0,<12.12.1.0a0
  - bzip2 >=1.0.8,<2.0a0
  - glog >=0.7.1,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc >=14
  - libgoogle-cloud >=2.39.0,<2.40.0a0
  - libgoogle-cloud-storage >=2.39.0,<2.40.0a0
  - libopentelemetry-cpp >=1.21.0,<1.22.0a0
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - orc >=2.1.3,<2.1.4.0a0
  - snappy >=1.2.2,<1.3.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - parquet-cpp <0.0a0
  - arrow-cpp <0.0a0
  - apache-arrow-proc =*=cpu
  license: Apache-2.0
  license_family: APACHE
  size: 6506254
  timestamp: 1753350876396
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-21.0.0-h635bf11_0_cpu.conda
  sha256: 4a4206e6a52ee25faf4faae77c1f0be438acc2f17c267a1da0309cf644287d89
  md5: 1f549118f553fda0889cff96f2ff1bdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 21.0.0 hd5bb725_0_cpu
  - libarrow-compute 21.0.0 he319acf_0_cpu
  - libgcc >=14
  - libstdcxx >=14
  license: Apache-2.0
  license_family: APACHE
  size: 659420
  timestamp: 1753351105968
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-compute-21.0.0-he319acf_0_cpu.conda
  sha256: 3ed0b683b6f9219b97ba550ffc977dc7e7ae093c11bfdc067d2efe1a28e88ccc
  md5: 901a69b8e4de174454a3f2bee13f118f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 21.0.0 hd5bb725_0_cpu
  - libgcc >=14
  - libre2-11 >=2024.7.2
  - libstdcxx >=14
  - libutf8proc >=2.10.0,<2.11.0a0
  - re2
  license: Apache-2.0
  license_family: APACHE
  size: 3119129
  timestamp: 1753350955329
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-21.0.0-h635bf11_0_cpu.conda
  sha256: c2a11b65e29bcfd801ede75e5d88626ad97cfe62f8f9fd149850cb12782a2622
  md5: 939fd9e5f73b435249268ddaa8425475
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 21.0.0 hd5bb725_0_cpu
  - libarrow-acero 21.0.0 h635bf11_0_cpu
  - libarrow-compute 21.0.0 he319acf_0_cpu
  - libgcc >=14
  - libparquet 21.0.0 h790f06f_0_cpu
  - libstdcxx >=14
  license: Apache-2.0
  license_family: APACHE
  size: 631187
  timestamp: 1753351196394
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-21.0.0-h3f74fd7_0_cpu.conda
  sha256: dbc68b9df8b517037e8f4f4259ca84c7838d4d9828a7e86f7f64fadbd01ca99c
  md5: 343b0daf0ddc4acb9abd3438ebaf31ad
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libarrow 21.0.0 hd5bb725_0_cpu
  - libarrow-acero 21.0.0 h635bf11_0_cpu
  - libarrow-dataset 21.0.0 h635bf11_0_cpu
  - libgcc >=14
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=14
  license: Apache-2.0
  license_family: APACHE
  size: 515096
  timestamp: 1753351229503
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_hfdb39a5_mkl.conda
  build_number: 32
  sha256: 7a04219d42b3b0b85ed9d019f481e4227efa2baa12ff48547758e90e2e208adc
  md5: eceb19ae9105bc4d0e8d5a321d66c426
  depends:
  - mkl >=2024.2.2,<2025.0a0
  constrains:
  - liblapack  3.9.0   32*_mkl
  - blas 2.132   mkl
  - liblapacke 3.9.0   32*_mkl
  - libcblas   3.9.0   32*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17657
  timestamp: 1750388671003
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
  sha256: 462a8ed6a7bb9c5af829ec4b90aab322f8bcd9d8987f793e6986ea873bbd05cf
  md5: cb98af5db26e3f482bebb80ce9d947d3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 69233
  timestamp: 1749230099545
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
  sha256: 3eb27c1a589cbfd83731be7c3f19d6d679c7a444c3ba19db6ad8bf49172f3d83
  md5: 1c6eecffad553bde44c5238770cfb7da
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 33148
  timestamp: 1749230111397
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
  sha256: 76e8492b0b0a0d222bfd6081cae30612aa9915e4309396fdca936528ccf314b7
  md5: 3facafe58f3858eb95527c7d3a3fc578
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 282657
  timestamp: 1749230124839
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_h372d94f_mkl.conda
  build_number: 32
  sha256: d0449cdfb6c6e993408375bcabbb4c9630a9b8750c406455ce3a4865ec7a321c
  md5: 68b55daaf083682f58d9b7f5d52aeb37
  depends:
  - libblas 3.9.0 32_hfdb39a5_mkl
  constrains:
  - liblapack  3.9.0   32*_mkl
  - liblapacke 3.9.0   32*_mkl
  - blas 2.132   mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17280
  timestamp: 1750388682101
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
  sha256: fd1d153962764433fe6233f34a72cdeed5dcf8a883a85769e8295ce940b5b0c5
  md5: c965a5aa0d5c1c37ffc62dff36e28400
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 20440
  timestamp: 1633683576494
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
  sha256: b6c5cf340a4f80d70d64b3a29a7d9885a5918d16a5cb952022820e6d3e79dc8b
  md5: 45f6713cb00f124af300342512219182
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 449910
  timestamp: 1749033146806
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
  sha256: 2e14399d81fb348e9d231a82ca4d816bf855206923759b69ad006ba482764131
  md5: a1cfcc585f0c42bf8d5546bb1dfb668d
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 427426
  timestamp: 1685725977222
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
  sha256: da2080da8f0288b95dd86765c801c6e166c4619b910b11f9a8446fb852438dc2
  md5: 4211416ecba1866fab0c6470986c22d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  size: 74811
  timestamp: 1752719572741
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
  sha256: 77dd1f1efd327e6991e87f09c7c97c4ae1cfbe59d9485c41d339d6391ac9c183
  md5: bfbca721fd33188ef923dfe9ba172f29
  depends:
  - libgfortran5 15.1.0 hcea5267_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 29057
  timestamp: 1750808257258
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
  sha256: eea6c3cf22ad739c279b4d665e6cf20f8081f483b26a96ddd67d4df3c88dfa0a
  md5: 530566b68c3b8ce7eec4cd047eae19fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 1565627
  timestamp: 1750808236464
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.39.0-hdb79228_0.conda
  sha256: d3341cf69cb02c07bbd1837968f993da01b7bd467e816b1559a3ca26c1ff14c5
  md5: a2e30ccd49f753fd30de0d30b1569789
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libcurl >=8.14.1,<9.0a0
  - libgcc >=14
  - libgrpc >=1.73.1,<1.74.0a0
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=14
  - openssl >=3.5.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.39.0 *_0
  license: Apache-2.0
  license_family: Apache
  size: 1307909
  timestamp: 1752048413383
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.39.0-hdbdcf42_0.conda
  sha256: 59eb8365f0aee384f2f3b2a64dcd454f1a43093311aa5f21a8bb4bd3c79a6db8
  md5: bd21962ff8a9d1ce4720d42a35a4af40
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc >=14
  - libgoogle-cloud 2.39.0 hdb79228_0
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  size: 804189
  timestamp: 1752048589800
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.73.1-h1e535eb_0.conda
  sha256: f91e61159bf2cb340884ec92dd6ba42a620f0f73b68936507a7304b7d8445709
  md5: 8075d8550f773a17288c7ec2cf2f2d56
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.34.5,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libgcc >=13
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.1,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.73.1
  license: Apache-2.0
  license_family: APACHE
  size: 8408884
  timestamp: 1751746547271
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h3d81e11_1002.conda
  sha256: 2823a704e1d08891db0f3a5ab415a2b7e391a18f1e16d27531ef6a69ec2d36b9
  md5: 56aacccb6356b6b6134a79cdf5688506
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libxml2 >=2.13.8,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 2425708
  timestamp: 1752673860271
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_hc41d3b0_mkl.conda
  build_number: 32
  sha256: dc1be931203a71f5c84887cde24659fdd6fda73eb8c6cf56e67b68e3c7916efd
  md5: 6dc827963c12f90c79f5b2be4eaea072
  depends:
  - libblas 3.9.0 32_hfdb39a5_mkl
  constrains:
  - liblapacke 3.9.0   32*_mkl
  - blas 2.132   mkl
  - libcblas   3.9.0   32*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  size: 17284
  timestamp: 1750388691797
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
  sha256: 3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee
  md5: c7e925f37e3b40d893459e625f6a53f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 91183
  timestamp: 1748393666725
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
  sha256: b0f2b3695b13a989f75d8fd7f4778e1c7aabe3b36db83f0fe80b2cd812c0e975
  md5: 19e57602824042dfd0446292ef90488b
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 647599
  timestamp: 1729571887612
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.21.0-hb9b0907_1.conda
  sha256: ba9b09066f9abae9b4c98ffedef444bbbf4c068a094f6c77d70ef6f006574563
  md5: 1c0320794855f457dea27d35c4c71e23
  depends:
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libcurl >=8.14.1,<9.0a0
  - libgrpc >=1.73.1,<1.74.0a0
  - libopentelemetry-cpp-headers 1.21.0 ha770c72_1
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libzlib >=1.3.1,<2.0a0
  - nlohmann_json
  - prometheus-cpp >=1.3.0,<1.4.0a0
  constrains:
  - cpp-opentelemetry-sdk =1.21.0
  license: Apache-2.0
  license_family: APACHE
  size: 885397
  timestamp: 1751782709380
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.21.0-ha770c72_1.conda
  sha256: b3a1b36d5f92fbbfd7b6426982a99561bdbd7e4adbafca1b7f127c9a5ab0a60f
  md5: 9e298d76f543deb06eb0f3413675e13a
  license: Apache-2.0
  license_family: APACHE
  size: 363444
  timestamp: 1751782679053
- conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-21.0.0-h790f06f_0_cpu.conda
  sha256: ba388c8de7c6e15732ef16f317156e0e73f354c8a920aa4dc0dff5f54eb66695
  md5: 0567d0cd584c49fdff1393529af77118
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 21.0.0 hd5bb725_0_cpu
  - libgcc >=14
  - libstdcxx >=14
  - libthrift >=0.22.0,<0.22.1.0a0
  - openssl >=3.5.1,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 1369341
  timestamp: 1753351072036
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h943b412_0.conda
  sha256: c7b212bdd3f9d5450c4bae565ccb9385222bf9bb92458c2a23be36ff1b981389
  md5: 51de14db340a848869e69c632b43cca7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 289215
  timestamp: 1751559366724
- conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-6.31.1-h9ef548d_1.conda
  sha256: b2a62237203a9f4d98bedb2dfc87b548cc7cede151f65589ced1e687a1c3f3b1
  md5: b92e2a26764fcadb4304add7e698ccf2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4015243
  timestamp: 1751690262221
- conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2025.07.22-h7b12aa8_0.conda
  sha256: 3d6c77dd6ce9b3d0c7db4bff668d2c2c337c42dc71a277ee587b30f9c4471fc7
  md5: f9ad3f5d2eb40a8322d4597dca780d82
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libgcc >=14
  - libstdcxx >=14
  constrains:
  - re2 2025.07.22.*
  license: BSD-3-Clause
  license_family: BSD
  size: 210939
  timestamp: 1753295040247
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsentencepiece-0.2.0-hb747028_12.conda
  sha256: f4437c009e397d2b35fb3e7f09cd30f2f8ff857eab637be62385c0e8437e78a4
  md5: 9a3e0c6893eb10d03a2476aa48944fb6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libgcc >=13
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 821255
  timestamp: 1751791197867
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.3-hee844dc_1.conda
  sha256: 8c4faf560815a6d6b5edadc019f76d22a45171eaa707a1f1d1898ceda74b2e3f
  md5: 18d2ac95b507ada9ca159a6bd73255f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=14
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  size: 936339
  timestamp: 1753262589168
- conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
  sha256: fa39bfd69228a13e553bd24601332b7cfeb30ca11a3ca50bb028108fe90a7661
  md5: eecce068c7e4eddeb169591baac20ac4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 304790
  timestamp: 1745608545575
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.22.0-h454ac66_1.conda
  sha256: 4888b9ea2593c36ca587a5ebe38d0a56a0e6d6a9e4bb7da7d9a326aaaca7c336
  md5: 8ed82d90e6b1686f5e98f8b7825a15ef
  depends:
  - __glibc >=2.17,<3.0.a0
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc >=14
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.1,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  size: 424208
  timestamp: 1753277183984
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.1-cpu_mkl_hf38bc2d_103.conda
  sha256: 457c2b8b76340a6c8537dc73e27f9c9daba22087ead80a7ea4d89e5ac4117ce7
  md5: cc613cc921fe87d8ecda7a7c8fafc097
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=14
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=14
  - libuv >=1.51.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.7
  - mkl >=2024.2.2,<2025.0a0
  - sleef >=3.8,<4.0a0
  constrains:
  - pytorch 2.7.1 cpu_mkl_*_103
  - pytorch-cpu 2.7.1
  - pytorch-gpu <0.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 58441117
  timestamp: 1752546304864
- conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h202a827_0.conda
  sha256: c4ca78341abb308134e605476d170d6f00deba1ec71b0b760326f36778972c0e
  md5: 0f98f3e95272d118f7931b6bef69bfe5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 83080
  timestamp: 1748341697686
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.51.0-hb9d3cd8_0.conda
  sha256: 770ca175d64323976c9fe4303042126b2b01c1bd54c8c96cafeaba81bdb481b8
  md5: 1349c022c92c5efd3fd705a79a5804d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 890145
  timestamp: 1748304699136
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
  sha256: 3aed21ab28eddffdaf7f804f49be7a7d701e8f0e46c856d801270b470820a37b
  md5: aea31d2e5b1091feca96fcfe945c3cf9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  size: 429011
  timestamp: 1752159441324
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-64/llguidance-1.1.1-py39hcb7a38b_0.conda
  noarch: python
  sha256: e383768d8b185a0ad4300da85b03a28b168e1e1b105e98ef228eacee17b48439
  md5: 8170c84720e50489da7ce56f354c899f
  depends:
  - __glibc >=2.17,<3.0.a0
  - _python_abi3_support 1.*
  - cpython >=3.9
  - libgcc >=14
  - python
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 2203531
  timestamp: 1753320684829
- conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.8-h4922eb0_0.conda
  sha256: 209050b372cf2103ac6a8fcaaf7f1b0d4dbb425395733b2e84f8949fa66b6ca7
  md5: dda42855e1d9a0b59e071e28a820d0f5
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - openmp 20.1.8|20.1.8.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 3214565
  timestamp: 1752565638114
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 167055
  timestamp: 1733741040117
- conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
  sha256: 0fbacdfb31e55964152b24d5567e9a9996e1e7902fb08eb7d91b5fd6ce60803a
  md5: fee3164ac23dfca50cfcc8b85ddefb81
  depends:
  - mdurl >=0.1,<1
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 64430
  timestamp: 1733250550053
- conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py313h8060acc_1.conda
  sha256: d812caf52efcea7c9fd0eafb21d45dadfd0516812f667b928bee50e87634fae5
  md5: 21b62c55924f01b6eef6827167b46acb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 24856
  timestamp: 1733219782830
- conda: https://conda.modular.com/max-nightly/linux-64/max-25.5.0.dev2025072805-release.conda
  noarch: python
  sha256: 276a2f96d6ea4111aeb6362024e268c91b237c4bb3cda1757cb656679e7b2e9a
  depends:
  - numpy >=1.18
  - python-gil >=3.9,<3.14
  - max-core ==25.5.0.dev2025072805 release
  - mojo-dev ==25.5.0.dev2025072805 release
  constrains:
  - click >=8.0.0
  - gguf >=0.17.1
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - llguidance >=1.0.1
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - typing-extensions >=4.12.2
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0
  - opentelemetry-exporter-prometheus >=0.50b0
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus_client >=0.21.0
  - protobuf >=6.31.1,<6.32.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - regex >=2024.11.6
  - scipy >=1.13.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  license: LicenseRef-Modular-Proprietary
  size: 31530384
  timestamp: 1753680256330
- conda: https://conda.modular.com/max-nightly/linux-64/max-core-25.5.0.dev2025072805-release.conda
  sha256: 8a445ca1a13b2f06679c420865e96341751e3cb4507ccc97d62bf57800133d61
  depends:
  - mojo ==25.5.0.dev2025072805 release
  license: LicenseRef-Modular-Proprietary
  size: 57355317
  timestamp: 1753680256330
- conda: https://conda.modular.com/max-nightly/noarch/max-pipelines-25.5.0.dev2025072805-release.conda
  noarch: python
  sha256: ea9c53123582016623ec60a971101b1b5b7abece92ef0c4ab8d5af8f259eb704
  depends:
  - click >=8.0.0
  - gguf >=0.17.1
  - hf-transfer >=0.1.9
  - huggingface_hub >=0.28.0
  - llguidance >=1.0.1
  - pillow >=11.0.0
  - psutil >=6.1.1
  - requests >=2.32.3
  - pytorch >=2.5.0
  - tqdm >=4.67.1
  - transformers >=4.52.4
  - typing-extensions >=4.12.2
  - uvicorn >=0.34.0
  - uvloop >=0.21.0
  - aiofiles >=24.1.0
  - asgiref >=3.8.1
  - fastapi >=0.115.3,<0.116
  - grpcio >=1.68.0
  - httpx >=0.28.1,<0.29
  - msgspec >=0.19.0
  - opentelemetry-api >=1.29.0
  - opentelemetry-exporter-otlp-proto-http >=1.27.0
  - opentelemetry-exporter-prometheus >=0.50b0
  - opentelemetry-sdk >=1.29.0,<2.0
  - prometheus_client >=0.21.0
  - protobuf >=6.31.1,<6.32.0
  - pydantic-settings >=2.7.1
  - pydantic
  - pyinstrument >=5.0.1
  - python-json-logger >=2.0.7
  - pyzmq >=26.3.0
  - regex >=2024.11.6
  - scipy >=1.13.0
  - sse-starlette >=2.1.2
  - starlette >=0.40.0,<0.41.3
  - taskgroup >=0.2.2
  - tokenizers >=0.19.0
  - max ==25.5.0.dev2025072805 release
  license: LicenseRef-Modular-Proprietary
  size: 9956
  timestamp: 1753680256330
- conda: https://conda.modular.com/max-nightly/noarch/mblack-25.5.0.dev2025072805-release.conda
  noarch: python
  sha256: 9baed776782749ecd0b7c33ee9b47123f8478e2ff1c29c00148749ab521f6abc
  depends:
  - python >=3.9
  - click >=8.0.0
  - mypy_extensions >=0.4.3
  - packaging >=22.0
  - pathspec >=0.9.0
  - platformdirs >=2
  - tomli >=1.1.0
  - typing_extensions >=v4.12.2
  - python
  license: MIT
  size: 131467
  timestamp: 1753680256330
- conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
  sha256: 78c1bbe1723449c52b7a9df1af2ee5f005209f67e40b6e1d3c7619127c43b1c7
  md5: 592132998493b3ff25fd7479396e8351
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 14465
  timestamp: 1733255681319
- conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha770c72_16.conda
  sha256: 9be33c297dd53e4eafef7c6ec597f1b4dee99296a768816d9bf793e2432a027f
  md5: 06fc17a281d2f71995f3bb58a7b7f4e5
  depends:
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - llvm-openmp >=20.1.8
  - tbb 2021.*
  license: LicenseRef-IntelSimplifiedSoftwareOct2022
  license_family: Proprietary
  size: 124933224
  timestamp: 1753395548401
- conda: https://conda.modular.com/max-nightly/noarch/modular-25.5.0.dev2025072805-release.conda
  noarch: python
  sha256: 995a3be707b3d9cff43294b2fed65be868922ac462ac4dd472e608d944ca5a63
  depends:
  - max-pipelines ==25.5.0.dev2025072805 release
  license: LicenseRef-Modular-Proprietary
  size: 9380
  timestamp: 1753680256330
- conda: https://conda.modular.com/max-nightly/linux-64/mojo-25.5.0.dev2025072805-release.conda
  sha256: 7f0bfa9ed2f803f2205bdab5560e2a28ba1da1acf62fbb23963a100f0730dc08
  license: LicenseRef-Modular-Proprietary
  size: 78190498
  timestamp: 1753680256329
- conda: https://conda.modular.com/max-nightly/linux-64/mojo-dev-25.5.0.dev2025072805-release.conda
  sha256: b80a1f2484ec45260fc3584d92509e78f119578da39411c46e116e9503f1b1a6
  depends:
  - python >=3.9
  - mojo ==25.5.0.dev2025072805 release
  - mblack ==25.5.0.dev2025072805 release
  - jupyter_client >=8.6.2,<8.7
  license: LicenseRef-Modular-Proprietary
  size: 86714653
  timestamp: 1753680256330
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
  sha256: 1bf794ddf2c8b3a3e14ae182577c624fa92dea975537accff4bc7e5fea085212
  md5: aa14b9a5196a6d8dd364164b7ce56acf
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpfr >=4.2.1,<5.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  size: 116777
  timestamp: 1725629179524
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
  sha256: f25d2474dd557ca66c6231c8f5ace5af312efde1ba8290a6ea5e1732a4e669c0
  md5: 2eeb50cab6652538eee8fc0bc3340c81
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  size: 634751
  timestamp: 1725746740014
- conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
  sha256: 7d7aa3fcd6f42b76bd711182f3776a02bef09a68c5f117d66b712a6d81368692
  md5: 3585aa87c43ab15b167b574cd73b057b
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 439705
  timestamp: 1733302781386
- conda: https://conda.anaconda.org/conda-forge/linux-64/msgspec-0.19.0-py313h536fd9c_1.conda
  sha256: 7b1a68ca3d6936fd17159a934fa9597947afe277f9035fdbd83e35620db9f041
  md5: ca4b310ea9ea29eeeda6bed6c8dcddec
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 213465
  timestamp: 1736368288714
- conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.6.3-py313h8060acc_0.conda
  sha256: 4eb75a352c57d7b260d57db52dc27965ca3f62b47ba39090f7927942da7a2f48
  md5: 0cabb3f2ba71300370fcebe973d9ae38
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 97053
  timestamp: 1751310779863
- conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.16-py313h536fd9c_1.conda
  sha256: 916c6fb35f30ef3631b43657a160528fabaa00b10fdd679b10500410b806ca39
  md5: 224f2700fa997c2d0d13328f3d02f4b6
  depends:
  - __glibc >=2.17,<3.0.a0
  - dill >=0.3.8
  - libgcc >=13
  - python >=3.13.0rc1,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 349052
  timestamp: 1724954746625
- conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
  sha256: 6ed158e4e5dd8f6a10ad9e525631e35cee8557718f83de7a4e3966b1f772c4b1
  md5: e9c622e0d00fa24a6292279af3ab6d06
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11766
  timestamp: 1745776666688
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.5-pyhe01879c_0.conda
  sha256: 02019191a2597865940394ff42418b37bc585a03a1c643d7cea9981774de2128
  md5: 16bff3d37a4f99e3aa089c36c2b8d650
  depends:
  - python >=3.11
  - python
  constrains:
  - numpy >=1.25
  - scipy >=1.11.2
  - matplotlib >=3.8
  - pandas >=2.0
  license: BSD-3-Clause
  license_family: BSD
  size: 1564462
  timestamp: 1749078300258
- conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
  sha256: e2fc624d6f9b2f1b695b6be6b905844613e813aa180520e73365062683fe7b49
  md5: d76872d096d063e226482c99337209dc
  license: MIT
  license_family: MIT
  size: 135906
  timestamp: 1744445169928
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py313hf6604e3_0.conda
  sha256: 4bb3beafb59f0e29e2e63028109e2cbbf9d10680be76192da2cea342d8892152
  md5: 34da5460bdcd8a5d360ef46cae9f626d
  depends:
  - python
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  size: 8889862
  timestamp: 1753401532585
- conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
  sha256: 5bee706ea5ba453ed7fd9da7da8380dd88b865c8d30b5aaec14d2b6dd32dbc39
  md5: 9e5816bc95d285c115a3ebc2f8563564
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 342988
  timestamp: 1733816638720
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.1-h7b32b05_0.conda
  sha256: 942347492164190559e995930adcdf84e2fea05307ec8012c02a505f5be87462
  md5: c87df2ab1448ba69169652ab9547082d
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3131002
  timestamp: 1751390382076
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-api-1.35.0-pyhd8ed1ab_0.conda
  sha256: 6228c870ad994ea843b78505c3df818dada38a6e9a8c658a02552898c8ddb218
  md5: 241b102f0e44e7992f58c2419b84cf2e
  depends:
  - deprecated >=1.2.6
  - importlib-metadata <8.8.0,>=6.0
  - python >=3.9
  - typing_extensions >=4.5.0
  license: Apache-2.0
  license_family: APACHE
  size: 45773
  timestamp: 1752286891826
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-common-1.35.0-pyhd8ed1ab_0.conda
  sha256: ff2776168c26365290ab480ac14f8f27392d4286c6f8fabd9c33884bd9fff094
  md5: d98d06fedf338be8773b6c9bb023952d
  depends:
  - backoff >=1.10.0,<3.0.0
  - opentelemetry-proto 1.35.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 19234
  timestamp: 1752327590965
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-http-1.35.0-pyhd8ed1ab_0.conda
  sha256: 41c96d6d309eedfd9c2ef49784e79ab0e228351fb9ef6ccbdb3839ac110fcb7c
  md5: 2582574aa069164d1127c0b84e31bf47
  depends:
  - deprecated >=1.2.6
  - googleapis-common-protos >=1.52,<2.dev0
  - opentelemetry-api >=1.15,<2.dev0
  - opentelemetry-exporter-otlp-proto-common 1.35.0
  - opentelemetry-proto 1.35.0
  - opentelemetry-sdk >=1.35.0,<1.36.dev0
  - python >=3.9
  - requests >=2.7,<3.dev0
  - typing_extensions >=4.5.0
  license: Apache-2.0
  license_family: APACHE
  size: 18011
  timestamp: 1752362461602
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-prometheus-0.56b0-pyhe01879c_0.conda
  sha256: 37bc08d2931dbcdb6863fa13c19bea05945067410e54d74bb4d0d39cf347f0c6
  md5: 3d4efe7ed89f0d193117b8e4c43aeeca
  depends:
  - python >=3.9
  - opentelemetry-api >=1.12,<2.dev0
  - opentelemetry-sdk >=1.35.0
  - prometheus_client >=0.5.0,<1.0.0
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 23265
  timestamp: 1752788373008
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-proto-1.35.0-pyhd8ed1ab_0.conda
  sha256: 53f20256a65df56031b8d285dd76c5181fe987682efe8286dd02f5fee31e3ce9
  md5: 67e3d4dd1e0ced032ef8fa99340e50c5
  depends:
  - protobuf <7.0,>=5.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 45741
  timestamp: 1752308297180
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-sdk-1.35.0-pyhd8ed1ab_0.conda
  sha256: f091363a1a0dd8d1c9b889f9ee433f28efb122edbc4222b8468790689fd106b1
  md5: 226ec4d220a74e1fcc8c658f365bd3ef
  depends:
  - opentelemetry-api 1.35.0
  - opentelemetry-semantic-conventions 0.56b0
  - python >=3.9
  - typing-extensions >=3.7.4
  - typing_extensions >=4.5.0
  license: Apache-2.0
  license_family: APACHE
  size: 78751
  timestamp: 1752299653515
- conda: https://conda.anaconda.org/conda-forge/noarch/opentelemetry-semantic-conventions-0.56b0-pyh3cfb1c2_0.conda
  sha256: 9d439ad39d33f3ea61553b5a48b4250fd06d8a4ad99ccb3bac6d8d1a273339ba
  md5: 251c0dfb684e8f43a71d579091191580
  depends:
  - deprecated >=1.2.6
  - opentelemetry-api 1.35.0
  - python >=3.9
  - typing_extensions >=4.5.0
  license: Apache-2.0
  license_family: APACHE
  size: 107441
  timestamp: 1752290820962
- conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.17.0-py313h7037e92_0.conda
  sha256: 5c5dadb01b461fd28aa193a86607a74df3b3a2751901c3dd654008299e76f47d
  md5: 21ca2b3ea73b2143033cd87ceadf270e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - typing-extensions >=4.6
  license: Apache-2.0
  license_family: Apache
  size: 454293
  timestamp: 1753455273044
- conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.3-h61e0c1e_0.conda
  sha256: 76b5d0efa288bc491a9d1c59bf9c3cf81aca420035de5c7166eed28029ccddfb
  md5: 451e93e0c51efff54f9e91d61187a572
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - snappy >=1.2.1,<1.3.0a0
  - tzdata
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  size: 1264711
  timestamp: 1752097610136
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.1-py313h08cd8bf_0.conda
  sha256: e7331b169835d8f22d7fc7dfa16c075de8a2e95245b89623097017a9cb87d623
  md5: 0b23bc9b44d838b88f3ec8ab780113f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.13,<3.14.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.13.* *_cp313
  - pytz >=2020.1
  constrains:
  - psycopg2 >=2.9.6
  - tzdata >=2022.7
  - blosc >=1.21.3
  - numexpr >=2.8.4
  - s3fs >=2022.11.0
  - zstandard >=0.19.0
  - pyxlsb >=1.0.10
  - qtpy >=2.3.0
  - xlrd >=2.0.1
  - numba >=0.56.4
  - matplotlib >=3.6.3
  - fastparquet >=2022.12.0
  - python-calamine >=0.1.7
  - bottleneck >=1.3.6
  - html5lib >=1.1
  - odfpy >=1.4.1
  - pytables >=3.8.0
  - fsspec >=2022.11.0
  - pyreadstat >=1.2.0
  - lxml >=4.9.2
  - sqlalchemy >=2.0.0
  - openpyxl >=3.1.0
  - beautifulsoup4 >=4.11.2
  - tabulate >=0.9.0
  - xlsxwriter >=3.0.5
  - xarray >=2022.12.0
  - gcsfs >=2022.11.0
  - scipy >=1.10.0
  - pandas-gbq >=0.19.0
  - pyarrow >=10.0.1
  - pyqt5 >=5.15.9
  license: BSD-3-Clause
  license_family: BSD
  size: 15120709
  timestamp: 1752082214786
- conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9f64009cdf5b8e529995f18e03665b03f5d07c0b17445b8badef45bde76249ee
  md5: 617f15191456cc6a13db418a275435e5
  depends:
  - python >=3.9
  license: MPL-2.0
  license_family: MOZILLA
  size: 41075
  timestamp: 1733233471940
- conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py313h8db990d_0.conda
  sha256: 73067c9a1ea4857ce9fb6788d404cd7d931ba323ad26eddf083c5b12dc8d73c0
  md5: 114a74a6e184101112fdffd3a1cb5b8f
  depends:
  - __glibc >=2.17,<3.0.a0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42651243
  timestamp: 1751482117433
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
  sha256: 013669433eb447548f21c3c6b16b2ed64356f726b5f77c1b39d5ba17a8a4b8bc
  md5: a83f6a2fdc079e643237887a37460668
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  license_family: MIT
  size: 199544
  timestamp: 1730769112346
- conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
  sha256: 454e2c0ef14accc888dd2cd2e8adb8c6a3a607d2d3c2f93962698b5718e6176d
  md5: c64b77ccab10b822722904d889fa83b5
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 52641
  timestamp: 1748896836631
- conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py313h8060acc_0.conda
  sha256: 49ec7b35291bff20ef8af0cf0a7dc1c27acf473bfbc121ccb816935b8bf33934
  md5: b62867739241368f43f164889b45701b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: APACHE
  size: 53174
  timestamp: 1744525061828
- conda: https://conda.anaconda.org/conda-forge/linux-64/protobuf-6.31.1-py313hc6d18d0_0.conda
  sha256: d109a116bae039bed3de46c7e947b8b5a3949a09702da997cd1153417b9b6a2d
  md5: 2af2437a6b4dcad58d277b2ded0b51b2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - libprotobuf 6.31.1
  license: BSD-3-Clause
  license_family: BSD
  size: 485269
  timestamp: 1751669105133
- conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py313h536fd9c_0.conda
  sha256: 1b39f0ce5a345779d70c885664d77b5f8ef49f7378829bd7286a7fb98b7ea852
  md5: 8f315d1fce04a046c1b93fa6e536661d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 475101
  timestamp: 1740663284505
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-21.0.0-py313h78bf25f_0.conda
  sha256: 5d0f17c0fbf8d5b78458bc616d5bfaf9812aa9cc81e9e38b21e61787a5060199
  md5: 1580ddd94606ccb60270877cb8838562
  depends:
  - libarrow-acero 21.0.0.*
  - libarrow-dataset 21.0.0.*
  - libarrow-substrait 21.0.0.*
  - libparquet 21.0.0.*
  - pyarrow-core 21.0.0 *_0_*
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  size: 26103
  timestamp: 1753372222314
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-21.0.0-py313he109ebe_0_cpu.conda
  sha256: 900374d98691caf4c9fffec8713b5ee2fc70040bd2dddbe1c91d660714bc89b5
  md5: 3018b7f30825c21c47a7a1e061459f96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 21.0.0.* *cpu
  - libarrow-compute 21.0.0.* *cpu
  - libgcc >=14
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - apache-arrow-proc * cpu
  - numpy >=1.21,<3
  license: Apache-2.0
  size: 5345643
  timestamp: 1753371833528
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-3.0.0-pyh9380348_1.conda
  sha256: 714eaae4187d31a25d8eef72784410bd2ad9155ec690756aa70d2cda1c35dee2
  md5: 309c97c5918389f181cc7d9c29e2a6e5
  depends:
  - python >=3.9
  - pybind11-global ==3.0.0 *_1
  - python
  constrains:
  - pybind11-abi ==11
  license: BSD-3-Clause
  license_family: BSD
  size: 231086
  timestamp: 1752769966512
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-3.0.0-pyhf748d72_1.conda
  sha256: 7ee5a97d9eb5a0fb0003a2c5d70ec2439c9bfb91fa1d0367efc75307ca5717f8
  md5: 5da3d3a7c804a3cd719448b81dd3dcb5
  depends:
  - python >=3.9
  - __unix
  - python
  constrains:
  - pybind11-abi ==11
  license: BSD-3-Clause
  license_family: BSD
  size: 227229
  timestamp: 1752769938071
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 110100
  timestamp: 1733195786147
- conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-2.11.7-pyh3cfb1c2_0.conda
  sha256: ee7823e8bc227f804307169870905ce062531d36c1dcf3d431acd65c6e0bd674
  md5: 1b337e3d378cde62889bb735c024b7a2
  depends:
  - annotated-types >=0.6.0
  - pydantic-core 2.33.2
  - python >=3.9
  - typing-extensions >=4.6.1
  - typing-inspection >=0.4.0
  - typing_extensions >=4.12.2
  license: MIT
  license_family: MIT
  size: 307333
  timestamp: 1749927245525
- conda: https://conda.anaconda.org/conda-forge/linux-64/pydantic-core-2.33.2-py313h4b2b08d_0.conda
  sha256: 754e3739e4b2a8856573e75829a1cccc0d16ee59dbee6ad594a70728a90e2854
  md5: 04b21004fe9316e29c92aa3accd528e5
  depends:
  - python
  - typing-extensions >=4.6.0,!=4.7.0
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 1894157
  timestamp: 1746625309269
- conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-settings-2.10.1-pyh3cfb1c2_0.conda
  sha256: e56b9a0320e3cab58b88f62ccdcd4bf7cd89ec348c878e1843d4d22315bfced1
  md5: a5f9c3e867917c62d796c20dba792cbd
  depends:
  - pydantic >=2.7.0
  - python >=3.9
  - python-dotenv >=0.21.0
  - typing-inspection >=0.4.0
  license: MIT
  license_family: MIT
  size: 38816
  timestamp: 1750801673349
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
  sha256: 5577623b9f6685ece2697c6eb7511b4c9ac5fb607c9babc2646c811b428fd46a
  md5: 6b6ece66ebcae2d5f326c77ef2c5a066
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 889287
  timestamp: 1750615908735
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyinstrument-5.0.3-py313h536fd9c_0.conda
  sha256: 063a2b192ab3e31d8cf17fe05aeaae705d8270dd6ced00453d98cf9147e8f316
  md5: 5cc959b1b4a62eb00d023ae1d1f5f92b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 186255
  timestamp: 1751480485691
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
  build_number: 102
  sha256: c2cdcc98ea3cbf78240624e4077e164dc9d5588eefb044b4097c3df54d24d504
  md5: 89e07d92cf50743886f41638d58c4328
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  size: 33273132
  timestamp: 1750064035176
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
  sha256: d6a17ece93bbd5139e02d2bd7dbfa80bee1a4261dced63f65f679121686bf664
  md5: 5b8d21249ff20967101ffa321cab24e8
  depends:
  - python >=3.9
  - six >=1.5
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 233310
  timestamp: 1751104122689
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dotenv-1.1.1-pyhe01879c_0.conda
  sha256: 9a90570085bedf4c6514bcd575456652c47918ff3d7b383349e26192a4805cc8
  md5: a245b3c04afa11e2e52a0db91550da7c
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 26031
  timestamp: 1750789290754
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.13.5-h4df99d1_102.conda
  sha256: ac6cf618100c2e0cad1cabfe2c44bf4a944aa07bb1dc43abff73373351a7d079
  md5: 2eabcede0db21acee23c181db58b4128
  depends:
  - cpython 3.13.5.*
  - python_abi * *_cp313
  license: Python-2.0
  size: 47572
  timestamp: 1750062593102
- conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
  sha256: 4790787fe1f4e8da616edca4acf6a4f8ed4e7c6967aa31b920208fc8f95efcca
  md5: a61bf9ec79426938ff785eb69dbb1960
  depends:
  - python >=3.6
  license: BSD-2-Clause
  license_family: BSD
  size: 13383
  timestamp: 1677079727691
- conda: https://conda.anaconda.org/conda-forge/noarch/python-multipart-0.0.20-pyhff2d567_0.conda
  sha256: 1b03678d145b1675b757cba165a0d9803885807792f7eb4495e48a38858c3cca
  md5: a28c984e0429aff3ab7386f7de56de6f
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 27913
  timestamp: 1734420869885
- conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
  sha256: e8392a8044d56ad017c08fec2b0eb10ae3d1235ac967d0aab8bd7b41c4a5eaf0
  md5: 88476ae6ebd24f39261e0854ac244f33
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 144160
  timestamp: 1742745254292
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py313h536fd9c_2.conda
  sha256: 24814b5a0162ef996c71d1eaad40b4be482c16a38a557dde93d3a413f1a96814
  md5: 992cc5204a44f3d2314c4c7912f4ac53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - xxhash >=0.8.3,<0.8.4.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 23426
  timestamp: 1740594911070
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-8_cp313.conda
  build_number: 8
  sha256: 210bffe7b121e651419cb196a2a63687b087497595c9be9d20ebe97dd06060a7
  md5: 94305520c52a4aa3f6c2b1ff6008d9f8
  constrains:
  - python 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 7002
  timestamp: 1752805902938
- conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.1-cpu_mkl_py313_h58dab0e_103.conda
  sha256: f01d1908dcca995454e5bd0f1658c89522926e4f3ddfb9fd82cf91cdbe732f37
  md5: 14fd59c6195a9d61987cf42e138b1a92
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - filelock
  - fsspec
  - jinja2
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=14
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libstdcxx >=14
  - libtorch 2.7.1 cpu_mkl_hf38bc2d_103
  - libuv >=1.51.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.7
  - mkl >=2024.2.2,<2025.0a0
  - networkx
  - numpy >=1.23,<3
  - optree >=0.13.0
  - pybind11
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - setuptools
  - sleef >=3.8,<4.0a0
  - sympy >=1.13.3
  - typing_extensions >=4.10.0
  constrains:
  - pytorch-cpu 2.7.1
  - pytorch-gpu <0.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 29585205
  timestamp: 1752550946935
- conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
  sha256: 8d2a8bf110cc1fc3df6904091dead158ba3e614d8402a83e51ed3a8aa93cdeb0
  md5: bc8e3267d44011051f2eb14d22fb0960
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 189015
  timestamp: 1742920947249
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py313h8060acc_2.conda
  sha256: 6826217690cfe92d6d49cdeedb6d63ab32f51107105d6a459d30052a467037a0
  md5: 50992ba61a8a1f8c2d346168ae1c86df
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 205919
  timestamp: 1737454783637
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.0-py313h8e95178_0.conda
  sha256: 6446721c43ba540c02ced4dde564f5a9a0131e40aa406e8af6313084c4a2024f
  md5: c912a00e5cb59357ef55b7930a48cf48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 384549
  timestamp: 1749898593849
- conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2025.07.22-h5a314c3_0.conda
  sha256: 0e65b369dad6b161912e58aaa20e503534225d999b2a3eeedba438f0f3923c7e
  md5: 40a7d4cef7d034026e0d6b29af54b5ce
  depends:
  - libre2-11 2025.07.22 h7b12aa8_0
  license: BSD-3-Clause
  license_family: BSD
  size: 27363
  timestamp: 1753295056377
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py313h536fd9c_0.conda
  sha256: 89a311b8f129e19493773788da338cdf342495f8482248142131a22422bf63ea
  md5: 2b80e596f4964f14b2317f8439a9df3e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Python-2.0
  license_family: PSF
  size: 403874
  timestamp: 1730952346266
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
  sha256: 9866aaf7a13c6cfbe665ec7b330647a0fb10a81e6f9b8fee33642232a1920e18
  md5: f6082eae112814f1447b56a5e1f6ed05
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  size: 59407
  timestamp: 1749498221996
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
  sha256: 3bda3cd6aa2ca8f266aeb8db1ec63683b4a7252d7832e8ec95788fb176d0e434
  md5: c41e49bd1f1479bed6c6300038c5466e
  depends:
  - markdown-it-py >=2.2.0
  - pygments >=2.13.0,<3.0.0
  - python >=3.9
  - typing_extensions >=4.0.0,<5.0.0
  - python
  license: MIT
  size: 201098
  timestamp: 1753436991345
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-toolkit-0.14.8-pyhe01879c_0.conda
  sha256: 8968e1ce998660929fdb5fc137331c00d6eacd3af2d8fee06fe6b23d65c09c51
  md5: 72f8778f6012d165c5cab305b5a4ae92
  depends:
  - python >=3.9
  - rich >=13.7.1
  - click >=8.1.7
  - typing_extensions >=4.12.2
  - python
  license: MIT
  license_family: MIT
  size: 26262
  timestamp: 1751941286394
- conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.23-h8e187f5_0.conda
  sha256: 016fe83763bc837beb205732411583179e2aac1cdef40225d4ad5eeb1bc7b837
  md5: edd15d7a5914dc1d87617a2b7c582d23
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - openssl >=3.5.1,<4.0a0
  license: Apache-2.0
  license_family: Apache
  size: 383097
  timestamp: 1753407970803
- conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py313h920b4c0_0.conda
  sha256: 13a54e151a12179f236e2c6d37c1c222cb975b68e641701f1d524fe510760442
  md5: 8567744ae20dfb943979146e3a45d812
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 432818
  timestamp: 1740651691391
- conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.0-py313h86fcf2b_0.conda
  sha256: 75bee2b5cb27616bcbd700d42dacc06577b90f1f9e31dc7682f4244867982a78
  md5: 8c60fe574a5abab59cd365d32e279872
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 16727241
  timestamp: 1751148531084
- conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-0.2.0-h9ebfe73_12.conda
  sha256: 631a92903fc5f2e8cd7fcdefab5efc5573f3b4c7513710f6b16bd28e5a57bb1a
  md5: ad4e94a74dd639f747b962a81d3ea863
  depends:
  - libsentencepiece 0.2.0 hb747028_12
  - python_abi 3.13.* *_cp313
  - sentencepiece-python 0.2.0 py313h4d3dc0d_12
  - sentencepiece-spm 0.2.0 hb747028_12
  license: Apache-2.0
  license_family: Apache
  size: 20085
  timestamp: 1751791602316
- conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-python-0.2.0-py313h4d3dc0d_12.conda
  sha256: 8c9a9e56039c0da0f37782eb7b3901af256dfe2693abd758fb26bf6262e01eb6
  md5: 1e887cca24cb24d8d4617cceee9e5cb2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libsentencepiece 0.2.0 hb747028_12
  - libstdcxx >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 2237289
  timestamp: 1751791450525
- conda: https://conda.anaconda.org/conda-forge/linux-64/sentencepiece-spm-0.2.0-hb747028_12.conda
  sha256: ac0b4080bb6274e53f7f42b9d2180708a3ce590a5c2949b79be6972acef90039
  md5: 36ab165391b5fd93b842e2af3061724a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250512.1,<20250513.0a0
  - libgcc >=13
  - libprotobuf >=6.31.1,<6.31.2.0a0
  - libsentencepiece 0.2.0 hb747028_12
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 90475
  timestamp: 1751791553783
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
  sha256: 972560fcf9657058e3e1f97186cc94389144b46dbdf58c807ce62e83f977e863
  md5: 4de79c071274a53dcaf2a8c749d1499e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 748788
  timestamp: 1748804951958
- conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
  sha256: 0557c090913aa63cdbe821dbdfa038a321b488e22bc80196c4b3b1aace4914ef
  md5: 7c3c2a0f3ebdea2bbc35538d162b43bf
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 14462
  timestamp: 1733301007770
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
  sha256: 458227f759d5e3fcec5d9b7acce54e10c9e1f4f4b7ec978f3bfd54ce4ee9853d
  md5: 3339e3b65d58accf4ca4fb8748ab16b3
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 18455
  timestamp: 1753199211006
- conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
  sha256: c998d5a29848ce9ff1c53ba506e7d01bbd520c39bbe72e2fb7cdf5a53bad012f
  md5: aec4dba5d4c2924730088753f6fa164b
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  license: BSL-1.0
  size: 1920152
  timestamp: 1738089391074
- conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.2-h03e3b7b_0.conda
  sha256: 8b8acbde6814d1643da509e11afeb6bb30eb1e3004cf04a7c9ae43e9b097f063
  md5: 3d8da0248bdae970b4ade636a104b7f5
  depends:
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  size: 45805
  timestamp: 1753083455352
- conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
  sha256: c2248418c310bdd1719b186796ae50a8a77ce555228b6acd32768e2543a15012
  md5: bf7a226e58dfb8346c70df36065d86c9
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 15019
  timestamp: 1733244175724
- conda: https://conda.anaconda.org/conda-forge/noarch/sse-starlette-2.1.3-pyhd8ed1ab_0.conda
  sha256: 6d671a66333410ec7e5e7858a252565a9001366726d1fe3c3a506d7156169085
  md5: 3918255c942c242ed5599e10329e8d0e
  depends:
  - anyio
  - python >=3.8
  - starlette
  - uvicorn
  license: BSD-3-Clause
  license_family: BSD
  size: 14712
  timestamp: 1722520112550
- conda: https://conda.anaconda.org/conda-forge/noarch/starlette-0.41.2-pyha770c72_0.conda
  sha256: 02206e5369944e0fd29e4f5c8e9b51dd926a74a46b621a73323669ad404f1081
  md5: 287492bb6e159da4357a10a2bd05c13c
  depends:
  - anyio >=3.4.0,<5
  - python >=3.8
  - typing_extensions >=3.10.0
  license: BSD-3-Clause
  license_family: BSD
  size: 59059
  timestamp: 1730305803101
- conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
  sha256: 09d3b6ac51d437bc996ad006d9f749ca5c645c1900a854a6c8f193cbd13f03a8
  md5: 8c09fac3785696e1c477156192d64b91
  depends:
  - __unix
  - cpython
  - gmpy2 >=2.0.8
  - mpmath >=0.19
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 4616621
  timestamp: 1745946173026
- conda: https://conda.anaconda.org/conda-forge/noarch/taskgroup-0.2.2-pyhd8ed1ab_0.conda
  sha256: 6f8db6da8de445930de55b708e6a5d3ab5f076bc14a39578db0190b2a9b8e437
  md5: 9fa69537fb68a095fbac139210575bad
  depends:
  - exceptiongroup
  - python >=3.9
  - typing_extensions >=4.12.2,<5
  license: MIT
  license_family: MIT
  size: 17330
  timestamp: 1736003478648
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
  sha256: 65463732129899770d54b1fbf30e1bb82fdebda9d7553caf08d23db4590cd691
  md5: ba7726b8df7b9d34ea80e82b097a4893
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  size: 175954
  timestamp: 1732982638805
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.3-py313h1191936_0.conda
  sha256: 05797340f493e27c6a24831d2accd9109f201a99c92fb05f2053e078d23c3863
  md5: 1df56575b9b40ccf176ff602af71f586
  depends:
  - __glibc >=2.17,<3.0.a0
  - huggingface_hub >=0.16.4,<1.0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.1,<4.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  size: 2366096
  timestamp: 1751646102266
- conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
  sha256: 18636339a79656962723077df9a56c0ac7b8a864329eb8f847ee3d38495b863e
  md5: ac944244f1fed2eb49bae07193ae8215
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 19167
  timestamp: 1733256819729
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py313h536fd9c_0.conda
  sha256: 282c9c3380217119c779fc4c432b0e4e1e42e9a6265bfe36b6f17f6b5d4e6614
  md5: e9434a5155db25c38ade26f71a2f5a48
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 873269
  timestamp: 1748003477089
- conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
  sha256: 11e2c85468ae9902d24a27137b6b39b4a78099806e551d390e394a8c34b48e40
  md5: 9efbfdc37242619130ea42b1cc4ed861
  depends:
  - colorama
  - python >=3.9
  license: MPL-2.0 or MIT
  size: 89498
  timestamp: 1735661472632
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.53.3-pyhd8ed1ab_0.conda
  sha256: 129f424dcf467b06b9dabdbb7c5de089f29cd9344d98de71a18a518e544d3132
  md5: 24e93054c9d236051151efedf87085ea
  depends:
  - datasets !=2.5.0
  - filelock
  - huggingface_hub >=0.30.0,<1.0
  - numpy >=1.17
  - packaging >=20.0
  - python >=3.9
  - pyyaml >=5.1
  - regex !=2019.12.17
  - requests
  - safetensors >=0.4.1
  - tokenizers >=0.21,<0.22
  - tqdm >=4.27
  license: Apache-2.0
  license_family: APACHE
  size: 3912110
  timestamp: 1753212255498
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
  sha256: 1ca70f0c0188598f9425a947afb74914a068bee4b7c4586eabb1c3b02fbf669f
  md5: 985cc086b73bda52b2f8d66dcda460a1
  depends:
  - typer-slim-standard ==0.16.0 hf964461_0
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 77232
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
  sha256: 54f859ddf5d3216fb602f54990c3ccefc65a30d1d98c400b998e520310630df3
  md5: 0d0a6c08daccb968c8c8fa93070658e2
  depends:
  - python >=3.9
  - click >=8.0.0
  - typing_extensions >=3.7.4.3
  - python
  constrains:
  - typer 0.16.0.*
  - rich >=10.11.0
  - shellingham >=1.3.0
  license: MIT
  license_family: MIT
  size: 46798
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
  sha256: c35a0b232e9751ac871b733d4236eee887f64c3b1539ba86aecf175c3ac3dc02
  md5: c8fb6ddb4f5eb567d049f85b3f0c8019
  depends:
  - typer-slim ==0.16.0 pyhe01879c_0
  - rich
  - shellingham
  license: MIT
  license_family: MIT
  size: 5271
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
  sha256: 349951278fa8d0860ec6b61fcdc1e6f604e6fce74fabf73af2e39a37979d0223
  md5: 75be1a943e0a7f99fcf118309092c635
  depends:
  - typing_extensions ==4.14.1 pyhe01879c_0
  license: PSF-2.0
  license_family: PSF
  size: 90486
  timestamp: 1751643513473
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-inspection-0.4.1-pyhd8ed1ab_0.conda
  sha256: 4259a7502aea516c762ca8f3b8291b0d4114e094bdb3baae3171ccc0900e722f
  md5: e0c3cd765dc15751ee2f0b03cd015712
  depends:
  - python >=3.9
  - typing_extensions >=4.12.0
  license: MIT
  license_family: MIT
  size: 18809
  timestamp: 1747870776989
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
  sha256: 4f52390e331ea8b9019b87effaebc4f80c6466d09f68453f52d5cdc2a3e1194f
  md5: e523f4f1e980ed7a4240d7e27e9ec81f
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  size: 51065
  timestamp: 1751643513473
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
  sha256: 4fb9789154bd666ca74e428d973df81087a697dbb987775bc3198d2215f240f8
  md5: 436c165519e140cb08d246a4472a9d6a
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  size: 101735
  timestamp: 1750271478254
- conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-0.35.0-pyh31011fe_0.conda
  sha256: bf304f72c513bead1a670326e02971c1cfe8320cf756447a45b74a2571884ad3
  md5: c7f6c7ffba6257580291ce55fb1097aa
  depends:
  - __unix
  - click >=7.0
  - h11 >=0.8
  - python >=3.9
  - typing_extensions >=4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 50232
  timestamp: 1751201685083
- conda: https://conda.anaconda.org/conda-forge/noarch/uvicorn-standard-0.35.0-h31011fe_0.conda
  sha256: 4eda451999a8358ab6242f1566123541315658226deda9a2af897c0bac164ef8
  md5: 9d5422831427100c32c50e6d33217b28
  depends:
  - __unix
  - httptools >=0.6.3
  - python-dotenv >=0.13
  - pyyaml >=5.1
  - uvicorn 0.35.0 pyh31011fe_0
  - uvloop >=0.14.0,!=0.15.0,!=0.15.1
  - watchfiles >=0.13
  - websockets >=10.4
  license: BSD-3-Clause
  license_family: BSD
  size: 7647
  timestamp: 1751201685854
- conda: https://conda.anaconda.org/conda-forge/linux-64/uvloop-0.21.0-py313h536fd9c_1.conda
  sha256: fa889930e1f6482a20a4ee12a8670e31b1c8704a6282b9172112379809d763c3
  md5: da0d10783ecd3b329ab2a16cab096980
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuv >=1.49.2,<2.0a0
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: MIT OR Apache-2.0
  size: 703782
  timestamp: 1730214503009
- conda: https://conda.anaconda.org/conda-forge/linux-64/watchfiles-1.1.0-py313h920b4c0_0.conda
  sha256: 5a27ec51a5de9a251ce7078739d0fc9301510992405fae5a29784c5daac3caeb
  md5: c9f7604d02c82ec812444d5ccee625bd
  depends:
  - __glibc >=2.17,<3.0.a0
  - anyio >=3.0.0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 420988
  timestamp: 1750054029977
- conda: https://conda.anaconda.org/conda-forge/linux-64/websockets-15.0.1-py313h536fd9c_0.conda
  sha256: 16c0e5acfa57f9fcd675b4210844305ff9552fac23bc6b30a937d0e920d3b6b3
  md5: 386e5d30ed6c4e5fb99d5a163a3f7028
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 273601
  timestamp: 1741285585784
- conda: https://conda.anaconda.org/conda-forge/linux-64/wrapt-1.17.2-py313h536fd9c_0.conda
  sha256: d0dafa5e2618e3fb6fccf5bfc3d3f65f29edc46582a7ebfcc231b61c1e6d61a9
  md5: e6795cc8e926da2e2abb634a46c4d60c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-2-Clause
  license_family: BSD
  size: 64497
  timestamp: 1736869638431
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
  sha256: 08e12f140b1af540a6de03dd49173c0e5ae4ebc563cabdd35ead0679835baf6f
  md5: 607e13a8caac17f9a664bcab5302ce06
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  size: 108219
  timestamp: 1746457673761
- conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h280c20c_3.conda
  sha256: 6d9ea2f731e284e9316d95fa61869fe7bbba33df7929f82693c121022810f4ad
  md5: a77f85f77be52ff59391544bfe73390a
  depends:
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  license: MIT
  size: 85189
  timestamp: 1753484064210
- conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.1-py313h8060acc_0.conda
  sha256: 4254322f6ed246ee3ddd6d4d80173ef44f8f82f3c2d31d9d23ce33849247ad94
  md5: b3659ec61a97eb6f64aeca04effb999d
  depends:
  - __glibc >=2.17,<3.0.a0
  - idna >=2.0
  - libgcc >=13
  - multidict >=4.0
  - propcache >=0.2.1
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: Apache-2.0
  license_family: Apache
  size: 149483
  timestamp: 1749554958820
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22963
  timestamp: 1749421737203
- conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
  sha256: 5d7c0e5f0005f74112a34a7425179f4eb6e73c92f5d109e6af4ddeca407c92ab
  md5: c9f075ab2f33b3bbee9e62d4ad0a6cd8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib 1.3.1 hb9d3cd8_2
  license: Zlib
  license_family: Other
  size: 92286
  timestamp: 1727963153079
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py313h536fd9c_2.conda
  sha256: ea9c542ef78c9e3add38bf1032e8ca5d18703114db353f6fca5c498f923f8ab8
  md5: a026ac7917310da90a98eac2c782723c
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.11
  - libgcc >=13
  - python >=3.13,<3.14.0a0
  - python_abi 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  size: 736909
  timestamp: 1745869790689
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 567578
  timestamp: 1742433379869
