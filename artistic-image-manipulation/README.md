# Artistic Image Manipulation System

A high-performance artistic image manipulation system built with Mojo, designed for creative image processing and artistic effects.

## Features

- **Core Image Processing**: Basic operations like resize, crop, rotate, flip
- **Artistic Filters**: Oil painting, watercolor, sketch, and other artistic effects
- **Color Manipulation**: HSV adjustments, color grading, palette extraction
- **Advanced Effects**: Blur effects, edge detection, texture synthesis
- **Performance Optimized**: Leverages Mojo's high-performance capabilities for fast processing

## Project Structure

```
artistic-image-manipulation/
├── src/
│   ├── core/
│   │   ├── image.mojo          # Core Image struct and basic operations
│   │   ├── pixel.mojo          # Pixel representation and operations
│   │   └── color.mojo          # Color space conversions and utilities
│   ├── filters/
│   │   ├── artistic.mojo       # Artistic filters (oil painting, watercolor, etc.)
│   │   ├── blur.mojo           # Various blur effects
│   │   ├── edge.mojo           # Edge detection algorithms
│   │   └── color_effects.mojo  # Color-based effects and adjustments
│   ├── utils/
│   │   ├── math_utils.mojo     # Mathematical utilities for image processing
│   │   ├── io.mojo             # Image I/O operations with Python interop
│   │   └── performance.mojo    # Performance optimization utilities
│   └── examples/
│       ├── basic_demo.mojo     # Basic usage examples
│       ├── artistic_demo.mojo  # Artistic effects demonstration
│       └── batch_process.mojo  # Batch processing example
├── tests/
│   └── test_*.mojo            # Unit tests for various modules
├── assets/
│   └── sample_images/         # Sample images for testing
└── README.md
```

## Getting Started

1. Ensure you have Mojo installed via the Modular platform
2. Install dependencies: `pixi install`
3. Run examples: `pixi run mojo src/examples/basic_demo.mojo`

## Usage Examples

### Basic Image Operations
```mojo
from src.core.image import Image
from src.utils.io import load_image, save_image

def main():
    # Load an image
    var img = load_image("input.jpg")
    
    # Resize the image
    var resized = img.resize(800, 600)
    
    # Apply artistic effect
    var artistic = resized.oil_painting_effect(brush_size=5)
    
    # Save the result
    save_image(artistic, "output.jpg")
```

### Advanced Artistic Effects
```mojo
from src.filters.artistic import WatercolorFilter, SketchFilter
from src.filters.color_effects import VintageFilter

def main():
    var img = load_image("portrait.jpg")
    
    # Apply watercolor effect
    var watercolor = WatercolorFilter.apply(img, intensity=0.7)
    
    # Convert to sketch
    var sketch = SketchFilter.apply(img, detail_level=3)
    
    # Apply vintage color grading
    var vintage = VintageFilter.apply(img, warmth=0.6, vignette=0.3)
```

## Performance

This system is designed to leverage Mojo's performance advantages:
- SIMD vectorization for pixel operations
- Efficient memory management
- Parallel processing capabilities
- Zero-cost abstractions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details
