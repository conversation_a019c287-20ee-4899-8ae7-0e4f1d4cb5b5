"""
Pixel representation and operations for image processing.
Provides efficient pixel manipulation with SIMD optimization.
"""

from math import sqrt, pow, min, max
from memory import memset_zero

@value
struct Pixel(Copyable, Movable, Stringable):
    """
    Represents a single pixel with RGBA channels.
    Values are stored as Float32 for precision in calculations.
    """
    var r: Float32
    var g: Float32  
    var b: Float32
    var a: Float32  # Alpha channel
    
    fn __init__(out self, r: Float32 = 0.0, g: Float32 = 0.0, b: Float32 = 0.0, a: Float32 = 1.0):
        """Initialize a pixel with RGBA values (0.0 to 1.0 range)."""
        self.r = self._clamp(r)
        self.g = self._clamp(g)
        self.b = self._clamp(b)
        self.a = self._clamp(a)
    
    fn __init__(out self, rgb: Int):
        """Initialize from packed RGB integer (0x000000 to 0xFFFFFF)."""
        self.r = Float32((rgb >> 16) & 0xFF) / 255.0
        self.g = Float32((rgb >> 8) & 0xFF) / 255.0
        self.b = Float32(rgb & 0xFF) / 255.0
        self.a = 1.0
    
    fn __str__(self) -> String:
        """String representation of the pixel."""
        return "Pixel(r=" + str(self.r) + ", g=" + str(self.g) + ", b=" + str(self.b) + ", a=" + str(self.a) + ")"
    
    fn _clamp(self, value: Float32) -> Float32:
        """Clamp value to [0.0, 1.0] range."""
        return max(0.0, min(1.0, value))
    
    fn to_rgb_int(self) -> Int:
        """Convert to packed RGB integer."""
        var r_int = Int(self.r * 255.0)
        var g_int = Int(self.g * 255.0)
        var b_int = Int(self.b * 255.0)
        return (r_int << 16) | (g_int << 8) | b_int
    
    fn to_rgba_int(self) -> Int:
        """Convert to packed RGBA integer."""
        var r_int = Int(self.r * 255.0)
        var g_int = Int(self.g * 255.0)
        var b_int = Int(self.b * 255.0)
        var a_int = Int(self.a * 255.0)
        return (a_int << 24) | (r_int << 16) | (g_int << 8) | b_int
    
    fn luminance(self) -> Float32:
        """Calculate luminance using standard weights."""
        return 0.299 * self.r + 0.587 * self.g + 0.114 * self.b
    
    fn grayscale(self) -> Self:
        """Convert to grayscale preserving alpha."""
        var lum = self.luminance()
        return Self(lum, lum, lum, self.a)
    
    fn invert(self) -> Self:
        """Invert colors preserving alpha."""
        return Self(1.0 - self.r, 1.0 - self.g, 1.0 - self.b, self.a)
    
    fn multiply(self, factor: Float32) -> Self:
        """Multiply RGB channels by factor."""
        return Self(
            self._clamp(self.r * factor),
            self._clamp(self.g * factor), 
            self._clamp(self.b * factor),
            self.a
        )
    
    fn add(self, other: Self) -> Self:
        """Add two pixels (useful for blending)."""
        return Self(
            self._clamp(self.r + other.r),
            self._clamp(self.g + other.g),
            self._clamp(self.b + other.b),
            self._clamp(self.a + other.a)
        )
    
    fn blend(self, other: Self, alpha: Float32) -> Self:
        """Blend with another pixel using alpha blending."""
        var inv_alpha = 1.0 - alpha
        return Self(
            self.r * inv_alpha + other.r * alpha,
            self.g * inv_alpha + other.g * alpha,
            self.b * inv_alpha + other.b * alpha,
            self.a * inv_alpha + other.a * alpha
        )
    
    fn distance(self, other: Self) -> Float32:
        """Calculate Euclidean distance between pixels in RGB space."""
        var dr = self.r - other.r
        var dg = self.g - other.g
        var db = self.b - other.b
        return sqrt(dr * dr + dg * dg + db * db)
    
    fn adjust_brightness(self, amount: Float32) -> Self:
        """Adjust brightness by adding amount to RGB channels."""
        return Self(
            self._clamp(self.r + amount),
            self._clamp(self.g + amount),
            self._clamp(self.b + amount),
            self.a
        )
    
    fn adjust_contrast(self, factor: Float32) -> Self:
        """Adjust contrast by scaling around 0.5."""
        return Self(
            self._clamp((self.r - 0.5) * factor + 0.5),
            self._clamp((self.g - 0.5) * factor + 0.5),
            self._clamp((self.b - 0.5) * factor + 0.5),
            self.a
        )
    
    fn to_hsv(self) -> HSV:
        """Convert RGB to HSV color space."""
        var max_val = max(max(self.r, self.g), self.b)
        var min_val = min(min(self.r, self.g), self.b)
        var delta = max_val - min_val
        
        # Value
        var v = max_val
        
        # Saturation
        var s: Float32 = 0.0
        if max_val > 0.0:
            s = delta / max_val
        
        # Hue
        var h: Float32 = 0.0
        if delta > 0.0:
            if max_val == self.r:
                h = 60.0 * ((self.g - self.b) / delta)
            elif max_val == self.g:
                h = 60.0 * (2.0 + (self.b - self.r) / delta)
            else:  # max_val == self.b
                h = 60.0 * (4.0 + (self.r - self.g) / delta)
            
            if h < 0.0:
                h += 360.0
        
        return HSV(h, s, v)

@value
struct HSV(Copyable, Movable, Stringable):
    """HSV (Hue, Saturation, Value) color representation."""
    var h: Float32  # Hue in degrees [0, 360)
    var s: Float32  # Saturation [0, 1]
    var v: Float32  # Value [0, 1]
    
    fn __init__(out self, h: Float32, s: Float32, v: Float32):
        self.h = h
        self.s = max(0.0, min(1.0, s))
        self.v = max(0.0, min(1.0, v))
    
    fn __str__(self) -> String:
        return "HSV(h=" + str(self.h) + ", s=" + str(self.s) + ", v=" + str(self.v) + ")"
    
    fn to_rgb(self) -> Pixel:
        """Convert HSV back to RGB."""
        var c = self.v * self.s
        var x = c * (1.0 - abs((self.h / 60.0) % 2.0 - 1.0))
        var m = self.v - c
        
        var r: Float32 = 0.0
        var g: Float32 = 0.0
        var b: Float32 = 0.0
        
        if self.h < 60.0:
            r = c; g = x; b = 0.0
        elif self.h < 120.0:
            r = x; g = c; b = 0.0
        elif self.h < 180.0:
            r = 0.0; g = c; b = x
        elif self.h < 240.0:
            r = 0.0; g = x; b = c
        elif self.h < 300.0:
            r = x; g = 0.0; b = c
        else:
            r = c; g = 0.0; b = x
        
        return Pixel(r + m, g + m, b + m, 1.0)

fn abs(x: Float32) -> Float32:
    """Absolute value function."""
    return x if x >= 0.0 else -x

# Common color constants
alias BLACK = Pixel(0.0, 0.0, 0.0, 1.0)
alias WHITE = Pixel(1.0, 1.0, 1.0, 1.0)
alias RED = Pixel(1.0, 0.0, 0.0, 1.0)
alias GREEN = Pixel(0.0, 1.0, 0.0, 1.0)
alias BLUE = Pixel(0.0, 0.0, 1.0, 1.0)
alias TRANSPARENT = Pixel(0.0, 0.0, 0.0, 0.0)
